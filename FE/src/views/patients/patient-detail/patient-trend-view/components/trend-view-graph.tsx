import clsx from 'clsx';
import {useEffect, useState} from 'react';

import {TooltipTrigger} from '@radix-ui/react-tooltip';
import {CartesianGrid, LabelList, Legend, Line, LineChart, ReferenceArea, XAxis, YAxis} from 'recharts';

import {But<PERSON>} from '@/components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent} from '@/components/ui/chart';
import {Switch} from '@/components/ui/switch.tsx';
import {Tooltip, TooltipContent} from '@/components/ui/tooltip';
import {ParameterSelection} from '@/views/patients/patient-detail/patient-trend-view/components/patient-trend-table.tsx';

interface ParameterData {
  name: string;
  id: string;
  dates: string[];
  values: (string | number | null)[];
  percentages: (string | number | null)[];
}

const parameterColors: Record<string, string> = {
  Weight: '#FF6B6B',
  BMI: '#4ECDC4',
  'Pre FEV1': '#45B7D1',
  'Pre FVC': '#96CEB4',
  'Pre VC': '#ffa923',
  'Pre FER (%)': '#D4A5A5',
  'Post FEV1': '#9B59B6',
  'Post FVC': '#3498DB',
  'Post VC': '#E67E22',
  'Post FER (%)': '#2ECC71',
  VA: '#F1C40F',
  TLCO: '#E74C3C',
  KCO: '#8E44AD',
  'TLCO Hb': '#16A085',
  'KCO Hb': '#D35400',
  TLC: '#2980B9',
  FRC: '#27AE60',
  RV: '#C0392B',
  'RV/TLC (%)': '#8D5524',
  MIP: '#00CED1',
  MEP: '#df90f4',
  SNIP: '#ff5800',
  FeNO: '#4682B4',
  'FEV1/FVC (%)': '#1ABC9C',
  'FEV1/VC (%)': '#F39C12',
  'FEF25-75': '#7F8C8D',
  PEF: '#34495E',
  Vi: '#8d6cc9',
  Hb: '#BDC3C7',
  'Vi/VC (%)': '#2C3E50',
  IC: '#95A5A6',
  ERV: '#D81B60',
  LvVC: '#8BC34A',
};

// Utility function to get Y-axis domain for zoom functionality
const getAxisYDomain = (data: any[], from: number, to: number, ref: string, offset: number) => {
  const refData = data.slice(from, to + 1);
  if (refData.length === 0) return ['dataMin', 'dataMax'];

  let bottom = refData[0][ref];
  let top = refData[0][ref];

  refData.forEach((d) => {
    if (d[ref] != null) {
      if (d[ref] > top) top = d[ref];
      if (d[ref] < bottom) bottom = d[ref];
    }
  });

  return [Math.max(0, bottom - offset), top + offset];
};

const transformData = (parameters: ParameterData[], type: 'absolute' | 'percentage') => {
  if (!parameters || parameters.length === 0) return [];

  const referenceDates = parameters[0]?.dates || [];

  const transformed = referenceDates.map((date, index) => {
    // Convert date string to timestamp for real-time spacing
    const timestamp = new Date(date).getTime();
    console.log('date: ', date);
    console.log('timestamp: ', timestamp, ' index: ', index);
    const dataPoint: Record<string, number | null | string> = {
      date,
      timestamp,
    };
    parameters.forEach((param) => {
      const value = type === 'percentage' ? param.percentages[index] : param.values[index];
      dataPoint[param.id] =
        value === '' || value === null || value === undefined || isNaN(Number(value)) ? null : Number(value);
    });
    return dataPoint;
  });

  return transformed;
};

const createChartConfig = (parameters: ParameterData[]): ChartConfig => {
  const config: ChartConfig = {};
  parameters.forEach((param) => {
    config[param.id] = {
      label: param.name,
      color: parameterColors[param.id] || '#000000',
    };
  });
  return config;
};

interface LabelPositionInfo {
  yOffset: number;
  height: number;
}

const calculateLabelPosition = (
  values: (string | number | null)[],
  index: number,
  y: number,
  threshold: number = 5,
  labelHeight: number = 15
): LabelPositionInfo => {
  if (index === 0 || values[index] === null || typeof values[index] === 'string') {
    return {yOffset: -labelHeight, height: labelHeight};
  }

  const currentValue = Number(values[index]);
  const positions: LabelPositionInfo[] = [];

  for (let i = 0; i < index; i++) {
    if (values[i] === null || typeof values[i] === 'string') {
      positions.push({yOffset: -labelHeight, height: labelHeight});
    } else {
      positions.push(calculateLabelPosition(values, i, y, threshold, labelHeight));
    }
  }

  let baseOffset = -labelHeight;
  let stackCount = 0;
  for (let i = index - 1; i >= 0; i--) {
    const prevValue = Number(values[i]);
    if (Math.abs(currentValue - prevValue) <= threshold) {
      const prevPos = positions[i];
      const prevTop = y + prevPos.yOffset;
      const prevBottom = prevTop + prevPos.height;

      if (baseOffset + y >= prevTop && baseOffset + y <= prevBottom) {
        stackCount++;
        baseOffset = prevPos.yOffset - labelHeight * stackCount;
      }
    } else {
      break;
    }
  }

  return {yOffset: baseOffset, height: labelHeight};
};

const CustomLabel = (props: any) => {
  const {x, y, value, index, parameter, type} = props;

  if (value === null || typeof value === 'string') return null;

  const positionInfo = calculateLabelPosition(
    type === 'percentages' ? parameter.percentages : parameter.values,
    index,
    y
  );

  return (
    <text
      x={x}
      y={y + positionInfo.yOffset}
      fill={parameterColors[parameter.id] || '#000000'}
      textAnchor="middle"
      fontSize={12}
    >
      {type === 'percentage' ? `${value}%` : value}
    </text>
  );
};

function EmptyState({title, description}: {title: string; description: string}) {
  return (
    <div className="table-empty min-h- flex h-full flex-1 flex-col items-center justify-center gap-5">
      <img
        src="/no-trend-selection.svg"
        alt="Empty Folder"
        className="h-32.5 w-32.5"
      />
      <div className="flex flex-col items-center justify-center gap-2">
        <div className="text-xl font-bold text-neutral-900">{title}</div>
        <div className="text-sm text-neutral-700">{description}</div>
      </div>
    </div>
  );
}

function TrendViewGraph({
                          id,
                          hidden = false,
                          type = 'absolute',
                          parameterSelection,
                          isLoading,
                        }: {
  hidden?: boolean;
  id?: string;
  type?: 'absolute' | 'percentage';
  parameterSelection?: any;
  isLoading?: boolean;
}) {
  if (!parameterSelection?.length) return null;

  const [chartType, setChartType] = useState<'absolute' | 'percentage'>('absolute');
  const chartData = transformData(parameterSelection, chartType);
  const chartConfig = createChartConfig(parameterSelection);

  // Zoom functionality state
  const [zoomState, setZoomState] = useState({
    left: 'dataMin' as string | number,
    right: 'dataMax' as string | number,
    refAreaLeft: '',
    refAreaRight: '',
    top: 'dataMax+10' as string | number,
    bottom: 'dataMin-10' as string | number,
  });

  const formatDateTime = (dateStr: string) => {
    const date = new Date(dateStr);
    return date
      .toLocaleString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      })
      .replace(/,/g, '');
  };

  // Zoom functionality
  const zoom = () => {
    let {refAreaLeft, refAreaRight} = zoomState;

    if (refAreaLeft === refAreaRight || refAreaRight === '') {
      setZoomState(prev => ({
        ...prev,
        refAreaLeft: '',
        refAreaRight: '',
      }));
      return;
    }

    // Find the indices for the selected area
    const leftIndex = chartData.findIndex(item => item.timestamp!.toString() === refAreaLeft);
    const rightIndex = chartData.findIndex(item => item.timestamp!.toString() === refAreaRight);

    if (leftIndex === -1 || rightIndex === -1) return;

    const fromIndex = Math.min(leftIndex, rightIndex);
    const toIndex = Math.max(leftIndex, rightIndex);

    // Calculate Y-axis domain for the first parameter (you can extend this for multiple parameters)
    const firstParam = parameterSelection[0];
    if (firstParam) {
      const [bottom, top] = getAxisYDomain(chartData, fromIndex, toIndex, firstParam.id, 10);

      setZoomState({
        refAreaLeft: '',
        refAreaRight: '',
        left: chartData[fromIndex].timestamp!.toString(),
        right: chartData[toIndex].timestamp!.toString(),
        bottom,
        top,
      });
    }
  };

  const zoomOut = () => {
    setZoomState({
      left: 'dataMin',
      right: 'dataMax',
      refAreaLeft: '',
      refAreaRight: '',
      top: 'dataMax+10',
      bottom: 'dataMin-10',
    });
  };

  useEffect(() => {
    if (parameterSelection.length > 1 && chartType === 'absolute') {
      setChartType('percentage');
    }
  }, [parameterSelection.length, chartType]);

  // Reset zoom when parameter selection changes
  useEffect(() => {
    setZoomState({
      left: 'dataMin',
      right: 'dataMax',
      refAreaLeft: '',
      refAreaRight: '',
      top: 'dataMax+10',
      bottom: 'dataMin-10',
    });
  }, [parameterSelection]);

  return (
    <Card
      id={parameterSelection?.length ? id : undefined}
      className={clsx('h-full border-neutral-200', hidden && 'transparent h-120')}
    >
      {!!parameterSelection?.length ? (
        <>
          <CardHeader
            className={clsx(
              'flex flex-row items-center justify-between border-b border-neutral-200 px-4 pt-3 pb-1',
              hidden && 'hidden'
            )}
          >
            <div className="flex items-center gap-4">
              <CardTitle className="text-xs font-semibold text-neutral-800 uppercase">
                Lung Function Trend
              </CardTitle>
              <Button
                variant="outlined"
                size="small"
                className="mb-0 py-1 text-xs"
                onPress={zoomOut}
              >
                Zoom Out
              </Button>
            </div>

            <div className="mb-2 flex items-center justify-end">
              <div className="flex items-center gap-x-2 text-xs uppercase">
                <label
                  htmlFor="chart-type"
                  className={clsx(
                    'transition duration-75',
                    chartType === 'absolute' ? 'text-gray-600' : 'text-gray-400'
                  )}
                >
                  absolute value
                </label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span>
                      <Switch
                        id="chart-type"
                        disabled={isLoading || (parameterSelection?.length > 1 && chartType === 'percentage')}
                        checked={chartType === 'percentage'}
                        onCheckedChange={() =>
                          setChartType(chartType === 'percentage' ? 'absolute' : 'percentage')
                        }
                      />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent
                    className={parameterSelection?.length === 1 ? 'hidden' : 'max-w-md whitespace-pre-wrap'}
                  >
                    {parameterSelection?.length > 1 && chartType === 'percentage'
                      ? 'Cannot display % change for multiple parameters'
                      : ''}
                  </TooltipContent>
                </Tooltip>
                <label
                  htmlFor="chart-type"
                  className={clsx(
                    'transition duration-75',
                    chartType === 'percentage' ? 'text-gray-600' : 'text-gray-400'
                  )}
                >
                  % change
                </label>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex h-full flex-1 flex-col px-6" style={{userSelect: 'none'}}>
            <ChartContainer
              className="h-full flex-1"
              config={chartConfig}
            >
              <LineChart
                accessibilityLayer
                data={chartData}
                margin={{top: 20, right: 30, left: 20, bottom: 20}}
                onMouseDown={(e) => {
                  if (e?.activeLabel) {
                    setZoomState(prev => ({...prev, refAreaLeft: e.activeLabel!.toString()}));
                  }
                }}
                onMouseMove={(e) => {
                  if (zoomState.refAreaLeft && e?.activeLabel) {
                    setZoomState(prev => ({...prev, refAreaRight: e.activeLabel!.toString()}));
                  }
                }}
                onMouseUp={zoom}
              >
                <CartesianGrid
                  strokeDasharray="10 10"
                  stroke="var(--color-neutral-200)"
                  vertical={false}
                />
                <XAxis
                  style={{fill: 'var(--color-neutral-600)'}}
                  dataKey="timestamp"
                  type="number"
                  scale="time"
                  domain={[zoomState.left, zoomState.right]}
                  // allowDataOverflow
                  tickLine={false}
                  axisLine={false}
                  tickMargin={20}
                  tickFormatter={(timestamp) => formatDateTime(new Date(timestamp).toISOString())}
                  height={50}
                  interval="preserveStartEnd"
                />
                <YAxis
                  style={{fill: 'var(--color-neutral-600)'}}
                  domain={[zoomState.bottom, zoomState.top]}
                  // allowDataOverflow
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => (chartType === 'percentage' ? `${value}%` : value)}
                  label={{
                    value: chartType === 'percentage' ? '% Change (ref = earliest)' : 'Absolute Value',
                    angle: -90,
                    position: 'left',
                    offset: 10,
                    style: {textAnchor: 'middle', fill: 'var(--color-neutral-600)'},
                    className: 'text-secondary text-[9px] leading-[18px] uppercase',
                  }}
                  dx={-15}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent />}
                />
                <Legend
                  align="right"
                  verticalAlign="top"
                  iconType="rect"
                  margin={{top: -200}}
                  height={40}
                />
                {/*{chartData.length > 10 && (*/}
                {/*    <Brush*/}
                {/*      dataKey="timestamp"*/}
                {/*      fill={'var(--color-brand2-200)'}*/}
                {/*      stroke={'var(--color-brand-500)'}*/}
                {/*      height={12}*/}
                {/*      tickFormatter={(timestamp) => formatDateTime(new Date(timestamp).toISOString())}*/}
                {/*    />*/}
                {/*)}*/}
                {parameterSelection.map((param: ParameterSelection) => (
                  <Line
                    key={param.id}
                    dataKey={param.id}
                    stroke={parameterColors[param.id] || '#000000'}
                    strokeWidth={2}
                    connectNulls={true}
                  >
                    <LabelList
                      dataKey={param.id}
                      offset={10}
                      content={
                        <CustomLabel
                          parameter={param}
                          type={type}
                        />
                      }
                    />
                  </Line>
                ))}

                {zoomState.refAreaLeft && zoomState.refAreaRight ? (
                  <ReferenceArea
                    x1={zoomState.refAreaLeft}
                    x2={zoomState.refAreaRight}
                    strokeOpacity={0.3}
                    fill="var(--color-brand-200)"
                    fillOpacity={0.3}
                  />
                ) : null}
              </LineChart>
            </ChartContainer>
          </CardContent>
        </>
      ) : (
        <div className="h-full p-30">
          <EmptyState
            description="select a parameter to display trend"
            title="Select one or more parameter"
          />
        </div>
      )}
    </Card>
  );
}

export default TrendViewGraph;

